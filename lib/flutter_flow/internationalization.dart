import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

const _kLocaleStorageKey = '__locale_key__';

class FFLocalizations {
  FFLocalizations(this.locale);

  final Locale locale;

  static FFLocalizations of(BuildContext context) =>
      Localizations.of<FFLocalizations>(context, FFLocalizations)!;

  static List<String> languages() => ['ar', 'en'];

  static late SharedPreferences _prefs;
  static Future initialize() async =>
      _prefs = await SharedPreferences.getInstance();
  static Future storeLocale(String locale) =>
      _prefs.setString(_kLocaleStorageKey, locale);
  static Locale? getStoredLocale() {
    final locale = _prefs.getString(_kLocaleStorageKey);
    return locale != null && locale.isNotEmpty ? createLocale(locale) : null;
  }

  String get languageCode => locale.toString();
  String? get languageShortCode =>
      _languagesWithShortCode.contains(locale.toString())
          ? '${locale.toString()}_short'
          : null;
  int get languageIndex =>
      languages().contains(languageCode)
          ? languages().indexOf(languageCode)
          : 0;

  String getText(String key) =>
      (kTranslationsMap[key] ?? {})[locale.toString()] ?? '';

  String getVariableText({String? arText = '', String? enText = ''}) =>
      [arText, enText][languageIndex] ?? '';

  static const Set<String> _languagesWithShortCode = {
    'ar',
    'az',
    'ca',
    'cs',
    'da',
    'de',
    'dv',
    'en',
    'es',
    'et',
    'fi',
    'fr',
    'gr',
    'he',
    'hi',
    'hu',
    'it',
    'km',
    'ku',
    'mn',
    'ms',
    'no',
    'pt',
    'ro',
    'ru',
    'rw',
    'sv',
    'th',
    'uk',
    'vi',
  };
}

/// Used if the locale is not supported by GlobalMaterialLocalizations.
class FallbackMaterialLocalizationDelegate
    extends LocalizationsDelegate<MaterialLocalizations> {
  const FallbackMaterialLocalizationDelegate();

  @override
  bool isSupported(Locale locale) => _isSupportedLocale(locale);

  @override
  Future<MaterialLocalizations> load(Locale locale) async =>
      SynchronousFuture<MaterialLocalizations>(
        const DefaultMaterialLocalizations(),
      );

  @override
  bool shouldReload(FallbackMaterialLocalizationDelegate old) => false;
}

/// Used if the locale is not supported by GlobalCupertinoLocalizations.
class FallbackCupertinoLocalizationDelegate
    extends LocalizationsDelegate<CupertinoLocalizations> {
  const FallbackCupertinoLocalizationDelegate();

  @override
  bool isSupported(Locale locale) => _isSupportedLocale(locale);

  @override
  Future<CupertinoLocalizations> load(Locale locale) =>
      SynchronousFuture<CupertinoLocalizations>(
        const DefaultCupertinoLocalizations(),
      );

  @override
  bool shouldReload(FallbackCupertinoLocalizationDelegate old) => false;
}

class FFLocalizationsDelegate extends LocalizationsDelegate<FFLocalizations> {
  const FFLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) => _isSupportedLocale(locale);

  @override
  Future<FFLocalizations> load(Locale locale) =>
      SynchronousFuture<FFLocalizations>(FFLocalizations(locale));

  @override
  bool shouldReload(FFLocalizationsDelegate old) => false;
}

Locale createLocale(String language) =>
    language.contains('_')
        ? Locale.fromSubtags(
          languageCode: language.split('_').first,
          scriptCode: language.split('_').last,
        )
        : Locale(language);

bool _isSupportedLocale(Locale locale) {
  final language = locale.toString();
  return FFLocalizations.languages().contains(
    language.endsWith('_')
        ? language.substring(0, language.length - 1)
        : language,
  );
}

final kTranslationsMap = <Map<String, Map<String, String>>>[
  // HomePage
  {
    'c3rvrahh': {'ar': 'عدد اللوطات:', 'en': 'Number of lots:'},
    'dezpohnt': {'ar': 'تفاصيل المزاد', 'en': 'Auction details'},
    '2cnwpqsa': {'ar': 'كراسه الشروط', 'en': 'Conditions booklet'},
    'jcus5ael': {'ar': 'القائمه', 'en': 'List'},
    'u1zjbppl': {'ar': 'الصفحه الشخصيه', 'en': 'Personal page'},
    'nwn11ho1': {'ar': 'من نحن', 'en': 'Who we are'},
    'j2p99ppn': {'ar': 'المساعده', 'en': 'Help'},
    'r0qv965d': {'ar': 'سياسة الخصوصية', 'en': 'privacy policy'},
    'n5kg97r5': {'ar': 'الشروط والأحكام', 'en': 'Terms and Conditions'},
    '38z9tocg': {'ar': 'تابعنا على ', 'en': 'Follow us on'},
    'emibmzr7': {'ar': 'Admin', 'en': 'Admin'},
    'dww3jrgg': {'ar': 'الصفحه الرئيسيه', 'en': 'Home page'},
  },
  // Profile2
  {
    '3k9emcsc': {'ar': ' وضع الضوء', 'en': 'Light mode'},
    'rahpoa0i': {'ar': 'الوضع المظلم', 'en': 'Dark mode'},
    'sct45f9n': {'ar': 'تسجيل الخروج', 'en': 'Sign out'},
    'cqppcow1': {'ar': 'انشاء حساب', 'en': 'Create an account'},
    'wxgfopgz': {'ar': 'حذف الحساب', 'en': 'Delete account'},
    'l1qq4ljn': {'ar': '__', 'en': '__'},
  },
  // AboutUs
  {
    'g8d5da9t': {
      'ar':
          'تطبيق \"الخبير  للمزادات\" هو منصة إلكترونية متخصصة في عرض معلومات المزادات المتاحة في السوق المصري، بهدف توفير تجربة موثوقة وآمنة للمستخدمين. يعرض التطبيق تفاصيل المزادات مثل مواعيد الانعقاد، أماكن المعاينة، والمستندات المطلوبة، دون تنفيذ أي مزايدات أو عمليات مالية داخل التطبيق.\n\n رؤيتنا\nنسعى لأن نكون الوجهة الرقمية الأولى لعرض معلومات المزادات في مصر، من خلال بيئة توفر الشفافية وسهولة الوصول للمعلومات، بما يخدم المهتمين بالشراء والاستثمار.\n\n قيمنا\nالاحترافية: نحرص على تقديم معلومات دقيقة ومحدثة بأعلى جودة.\n\nالشفافية: نوضح كافة التفاصيل المرتبطة بكل مزاد بشكل مبسط.\n\nالابتكار: نسعى لتطوير المنصة بشكل مستمر لتلبية احتياجات المستخدمين.\n\nالأمان: نحافظ على خصوصية بيانات المستخدمين ونلتزم بأعلى معايير الحماية.\n\n👥 فريق العمل\nيتكون فريق \"الخبير  للمزادات\" من مجموعة من المختصين في مجالات تقنية المعلومات، التسويق الرقمي، وإدارة المحتوى، مما يُمكننا من تقديم تطبيق عالي الجودة يلبي تطلعات المستخدمين.',
      'en':
          'The \"Al-Khabir Auctions\" app is an online platform specializing in displaying information about auctions available in the Egyptian market, aiming to provide a reliable and secure experience for users. The app displays auction details, such as dates, viewing locations, and required documents, without any bidding or financial transactions being conducted within the app.\n\nOur Vision\nWe strive to be the premier digital destination for displaying auction information in Egypt, through an environment that provides transparency and easy access to information, serving those interested in buying and investing.\n\nOur Values\nProfessionalism: We are committed to providing accurate, up-to-date information of the highest quality.\n\nTransparency: We explain all details related to each auction in a simplified manner.\n\nInnovation: We strive to continuously develop the platform to meet user needs.\n\nSecurity: We maintain the privacy of user data and adhere to the highest standards of protection.\n\n👥 Team\nThe \"Al-Khabir Auctions\" team consists of a group of specialists in the fields of information technology, digital marketing, and content management, enabling us to provide a high-quality application that meets user expectations.',
    },
    'fpwl14dc': {'ar': 'الإداريون', 'en': 'administrators'},
    'y8kj3okp': {'ar': 'مصطفي مختار', 'en': 'Mustafa Mukhtar'},
    'oh63mamb': {
      'ar': 'رئيس مجلس الاداره',
      'en': 'Chairman of the Board of Directors',
    },
    '3taom02c': {'ar': 'محمد سمير', 'en': 'Mohamed Samir'},
    '6hc18q0a': {
      'ar': 'خبير و مستشار قانوني',
      'en': 'Legal expert and advisor',
    },
    'lf2rvf6r': {'ar': 'محمد رشاد', 'en': 'Mohamed Rashad'},
    'qcbqcgwn': {
      'ar': 'خبير و مستشار قانوني',
      'en': 'Legal expert and advisor',
    },
    '60wwxfrf': {'ar': 'محمد علي عبدالرحمن', 'en': 'Mohammed Ali Abdel Rahman'},
    'plpwa8ph': {'ar': 'مسؤول تعاقدات', 'en': 'Contracting Officer'},
    'pgy2cbrr': {'ar': 'عمر مجدي', 'en': 'Omar Magdy'},
    'i4dut9kq': {'ar': 'محاسب قانوني', 'en': 'Chartered Accountant'},
    'deuxb0bz': {'ar': 'محمود الحسيني ', 'en': 'Mahmoud Al-Husseini'},
    'o0rfdmqu': {'ar': 'مدير التسويق', 'en': 'Marketing Manager'},
    'lfgc2djb': {'ar': 'من نحن', 'en': 'Who we are'},
    'uumvtd1r': {'ar': 'Home', 'en': 'Home'},
  },
  // SupportPage
  {
    'q0ong2xo': {
      'ar': 'اهلا بيك في قسم المساعده',
      'en': 'Welcome to the help section',
    },
    'her6il5p': {'ar': 'الاسئله الشائعه', 'en': 'Frequently Asked Questions'},
    'd3risas5': {'ar': 'هل التسجيل مجاني؟', 'en': 'Is registration free?'},
    '1jeodp38': {
      'ar':
          'نعم، التسجيل مجاني تمامًا ولا توجد أي رسوم على الخدمات داخل التطبيق.',
      'en':
          'Yes, registration is completely free and there are no fees for in-app services.',
    },
    'zxrqpptu': {
      'ar': 'ما هو هدف التطبيق ؟',
      'en': 'What is the purpose of the application?',
    },
    '1f62j161': {
      'ar':
          'التطبيق يساعدك في تصفح المزادات بكل سهوله في مكان واحد مع تنبيه عند اضافه مزاد جديد.',
      'en':
          'The app helps you browse auctions easily in one place, with alerts when a new auction is added.',
    },
    'jqr3olmd': {'ar': 'هل يوجد دعم فني؟', 'en': 'Is there technical support?'},
    'frb8ugv9': {
      'ar':
          'نعم، يوفر التطبيق دعمًا فنيًا عبر الواتساب  أو البريد الإلكتروني أو الهاتف.',
      'en':
          'Yes, the app provides technical support via WhatsApp, email, or phone.',
    },
    'co8pmich': {'ar': 'تواصل معنا', 'en': 'Contact us'},
    'txnalayn': {'ar': 'راسلنا عبر البريد الإلكتروني', 'en': 'Email us'},
    'rtpso8uk': {'ar': 'المحادثه الان', 'en': 'Chat now'},
    'gtsb6kfs': {'ar': 'المساعده', 'en': 'Help'},
    'kdzq7mmq': {'ar': 'Home', 'en': 'Home'},
  },
  // terms
  {
    'ck4uih28': {
      'ar':
          '📄 شروط وأحكام الاستخدام\nآخر تحديث: 30 يونيو 2025\n\nيرجى قراءة هذه الشروط والأحكام بعناية قبل استخدام تطبيق \"دليل المزادات المصرية\".\n\n1. مقدمة\nتطبيق \"دليل المزادات المصرية\" هو منصة إلكترونية لعرض معلومات حول المزادات العقارية، الصناعية، والتجارية التي يتم تنظيمها من خلال جهة مرخصة في مصر. باستخدامك للتطبيق، فإنك توافق على الالتزام بهذه الشروط.\n\n2. طبيعة الخدمة\nالتطبيق يتيح للمستخدمين تصفح معلومات عامة عن المزادات مثل الصور، التفاصيل، مواعيد الانعقاد، وأماكن المعاينة.\n\nلا يتم إجراء المزايدة أو البيع أو الدفع الإلكتروني داخل التطبيق.\n\nالتطبيق لا يُعد وسيطًا ماليًا أو منصة للمعاملات الإلكترونية.\n\n3. تسجيل المستخدم\nيمكن للمستخدم التصفح دون تسجيل، مع إمكانية إنشاء حساب للوصول إلى مزايا إضافية مثل تحميل كراسة الشروط.\n\nالمستخدم مسؤول عن صحة ودقة البيانات التي يقدمها عند التسجيل.\n\n4. إخلاء المسؤولية\nالتطبيق لا يتحمل مسؤولية أي قرارات تتخذ بناءً على المعلومات المعروضة.\n\nلمزيد من التفاصيل حول المزادات، يُرجى التواصل مع الجهة المنظمة للمزاد المذكور داخل التطبيق.\n\n5. حقوق النشر والملكية\nجميع المحتويات داخل التطبيق (الصور، النصوص، التصميمات) مملوكة لتطبيق \"دليل المزادات المصرية\"، ويُمنع استخدامها دون إذن مسبق.\n\n6. التحديثات\nيحتفظ فريق إدارة التطبيق بحق تعديل أو تحديث المحتوى أو الوظائف في أي وقت دون إشعار مسبق.\n\n7. تعديل الشروط\nقد يتم تعديل هذه الشروط من وقت لآخر. استمرار استخدامك للتطبيق يعني موافقتك على النسخة المحدثة منها.\n\n8. التواصل\nللاستفسارات أو الدعم، يُرجى التواصل معنا من خلال إعدادات التطبيق أو البريد الإلكتروني.\n',
      'en':
          '📄 Terms and Conditions of Use\nLast updated: June 28, 2025\n\nPlease read these terms and conditions carefully before using the \"Al-Khabir Auctions\" application.\n\n1. Introduction\nThe \"Al-Khabir Auctions\" application is an electronic platform for displaying information about real estate, industrial, and commercial auctions organized by a licensed entity in Egypt. By using the application, you agree to be bound by these terms.\n\n2. Nature of Service\nThe application allows users to browse general information about auctions, such as images, details, dates, and viewing locations.\n\nNo bidding, selling, or electronic payments take place within the application.\n\nThe application is not a financial intermediary or platform for electronic transactions.\n\n3. User Registration\nUsers can browse without registering, but can create an account to access additional features such as downloading the terms and conditions booklet.\n\nThe user is responsible for the accuracy and validity of the information they provide upon registration.\n\n4. Disclaimer\nThe application is not responsible for any decisions made based on the information displayed.\n\nFor more details about auctions, please contact the auction organizer mentioned within the app.\n\n5. Copyright and Ownership\nAll content within the app (images, texts, designs) is owned by the \"Al-Khabir Auctions\" app and may not be used without prior permission.\n\n6. Updates\nThe app management team reserves the right to modify or update content or functionality at any time without prior notice.\n\n7. Amendments to Terms\nThese terms may be modified from time to time. Your continued use of the app constitutes your acceptance of the updated version.\n\n8. Communication\nFor inquiries or support, please contact us via:\n📞 *********** / *********** / ***********',
    },
    'fkxkefg4': {'ar': 'الرجوع', 'en': 'Back'},
    'iqevqmme': {'ar': 'الشروط والأحكام', 'en': 'Terms and Conditions'},
    '9b87y00l': {'ar': 'Home', 'en': 'Home'},
  },
  // privecy
  {
    '4w6seacy': {
      'ar':
          'نحن في \"دليل المزادات المصرية\" نلتزم بحماية خصوصيتك وبياناتك. إليك كيفية تعاملنا مع معلوماتك:\n\n1. ما هي البيانات التي نجمعها؟\nمعلومات الحساب: مثل الاسم، البريد الإلكتروني، ورقم الهاتف (عند الحاجة للتحقق الأمني).\n\nبيانات الاستخدام: مثل المزادات التي تتابعها، ومدة استخدامك للتطبيق.\n\n🔹 نؤكد أننا لا نجمع أي مستندات رسمية (مثل البطاقة الشخصية) أو بيانات مالية حساسة.\n\n2. لماذا نستخدم بياناتك؟\nلتوفير الوظائف الأساسية داخل التطبيق.\n\nلإرسال إشعارات مهمة تختار أنت استلامها.\n\nلتحسين أدائنا وتجربة المستخدم بشكل مستمر.\n\n3. مع من نشارك بياناتك؟\nلا نبيع بياناتك لأي طرف.\n\nنشارك فقط مع مزودي خدمات تقنيين موثوقين (مثل خدمات قواعد البيانات أو الإشعارات)، وفقًا لاتفاقيات خصوصية صارمة.\n\n4. حقوقك الكاملة:\nيمكنك الوصول إلى بياناتك الشخصية، أو تعديلها، أو حذف حسابك في أي وقت عبر إعدادات التطبيق أو بالتواصل معنا مباشرة.\n\n5. للمزيد من التفاصيل:\nيرجى الاطلاع على سياسة الخصوصية الكاملة داخل التطبيق أو التواصل معنا من خلال البريد الإلكتروني.\n\n',
      'en':
          'We at Al-Khabir Auctions are committed to protecting your privacy and data. Here\'s how we handle your information:\n\n1. What data do we collect?\nAccount information: such as name, email, and phone number (if required for security verification).\n\nUsage data: such as the auctions you follow and the duration of your use of the app.\n\n🔹 We confirm that we do not collect any official documents (such as your ID card) or sensitive financial data.\n\n2. Why do we use your data?\nTo provide basic functionality within the app.\n\nTo send important notifications that you choose to receive.\n\nTo continuously improve our performance and user experience.\n\n3. Who do we share your data with?\nWe do not sell your data to any third party.\n\nWe only share it with trusted technical service providers (such as database or notification services), subject to strict privacy agreements.\n\n4. Your full rights:\nYou can access, edit, or delete your personal data at any time through the app settings or by contacting us directly.\n\n5. For more details:\nPlease review the full privacy policy within the app or contact us at \n📞 *********** / *********** / ***********',
    },
    'jdue74sp': {'ar': 'سياسه الخصوصيه', 'en': 'privacy policy'},
    'ke6vka1k': {'ar': 'الرجوع', 'en': 'Back'},
    'njytl7zt': {'ar': 'سياسة الخصوصية', 'en': 'privacy policy'},
    'bftphdfz': {'ar': 'Home', 'en': 'Home'},
  },
  // Aucations
  {
    '6hdk64rx': {'ar': 'Option 1', 'en': 'Option 1'},
    '7y7ev0z8': {'ar': 'Option 2', 'en': 'Option 2'},
    'oo6h31w3': {'ar': 'Option 3', 'en': 'Option 3'},
    '07vbm9to': {'ar': 'عدد اللوطات:', 'en': 'Number of lots:'},
    'd9vn3w02': {'ar': 'تفاصيل المزاد', 'en': 'Auction details'},
    'jcjegfdf': {'ar': 'كراسه الشروط', 'en': 'Conditions booklet'},
    'yd7urk8c': {'ar': 'المزادات', 'en': 'Auctions'},
    'hl0xkkmc': {'ar': 'المزادات', 'en': 'Auctions'},
  },
  // Lots
  {
    '1rrxw99x': {'ar': 'تفاصيل المزاد', 'en': 'Auction details'},
    'ardjow9s': {'ar': 'Home', 'en': 'Home'},
  },
  // Admin
  {
    'tmof98do': {'ar': 'اضافه مزاد', 'en': 'Add auction'},
    'a5089pfi': {'ar': 'اضافه اللوطات', 'en': 'Add lots'},
    'vbkg69cz': {'ar': 'المسؤلين', 'en': 'Officials'},
    '3yvw005l': {'ar': 'العملاء', 'en': 'Clients'},
    'ifkhr485': {'ar': 'ارسال الاشعارات', 'en': 'Send notifications'},
    '4xbxvkpl': {'ar': 'حذف', 'en': 'delete'},
    '6nhg017p': {'ar': 'اللوطات', 'en': 'Lots'},
    'uru0bqq8': {'ar': 'Admin', 'en': 'Admin'},
    '36v9veg4': {'ar': 'Home', 'en': 'Home'},
  },
  // LoutsEdit
  {
    'p3qpnaut': {'ar': 'حذف', 'en': 'delete'},
    'w52224b5': {'ar': 'اللوطات', 'en': 'Lots'},
    '09spagg9': {'ar': 'Home', 'en': 'Home'},
  },
  // Auth3
  {
    '403x5eyr': {'ar': 'إنشاء حساب', 'en': 'Create an account'},
    'vlxcy2j5': {'ar': 'إنشاء حساب', 'en': 'Create an account'},
    'hq0wm2yv': {
      'ar': 'لنبدأ بملء النموذج أدناه.',
      'en': 'Let\'s start by filling out the form below.',
    },
    'vah9h9uq': {'ar': 'الاسم', 'en': 'the name'},
    '55flrp4w': {'ar': 'هذا الحقل مطلوب*', 'en': 'This field is required*'},
    'u3y321ts': {'ar': 'البريد الالكتروني', 'en': 'e-mail'},
    'hfpajlps': {'ar': 'رقم الهاتف', 'en': 'phone number'},
    'q0csb0am': {'ar': 'هذا الحقل مطلوب*', 'en': 'This field is required*'},
    '3e0bbuay': {'ar': 'كلمة المرور', 'en': 'password'},
    '0tdtisgh': {'ar': 'هذا الحقل مطلوب*', 'en': 'This field is required*'},
    'r8j98i1r': {'ar': 'تأكيد كلمه المرور', 'en': 'Confirm password'},
    'rdg2ovf2': {'ar': 'أوافق على   ', 'en': 'I agree to'},
    '4na46o6n': {'ar': 'الشروط والأحكام', 'en': 'Terms and Conditions'},
    '4xjmthu4': {'ar': 'إنشاء حساب', 'en': 'Create an account'},
    '4kro8u3y': {'ar': 'تخطي', 'en': 'skip'},
    '0qo8a74t': {'ar': 'تسجيل الدخول', 'en': 'Log in'},
    'zjfjmu6g': {'ar': 'مرحبًا بعودتك', 'en': 'welcome back'},
    '1bii7r9t': {
      'ar': 'قم بملء المعلومات أدناه لتتمكن من الوصول إلى حسابك.',
      'en': 'Fill in the information below to access your account.',
    },
    'i2poc0sw': {'ar': 'بريد إلكتروني', 'en': 'Email'},
    '92w49cu1': {'ar': 'كلمة المرور', 'en': 'password'},
    'fagv4n3o': {'ar': 'تسجيل الدخول', 'en': 'Log in'},
    'lergt3on': {'ar': 'نسيت كلمه السر', 'en': 'Forgot your password'},
    'k0uvf2ui': {'ar': 'Home', 'en': 'Home'},
  },
  // forgetpass
  {
    'eesl97zv': {'ar': 'Back', 'en': 'Back'},
    'a4el66ro': {
      'ar': 'هل نسيت كلمة السر',
      'en': 'Did you forget your password?',
    },
    'lkl0r48d': {
      'ar':
          'سنرسل إليك رسالة بريد إلكتروني تحتوي على رابط لإعادة تعيين كلمة المرور الخاصة بك، يرجى إدخال البريد الإلكتروني المرتبط بحسابك أدناه.',
      'en':
          'We will send you an email with a link to reset your password. Please enter the email address associated with your account below.',
    },
    'fn9bew16': {
      'ar': 'عنوان بريدك  الإلكتروني...',
      'en': 'Your email address...',
    },
    '4068z4in': {'ar': 'أدخل بريدك الإلكتروني...', 'en': 'Enter your email...'},
    'alecd6qr': {'ar': 'ارسال', 'en': 'send'},
    'sfrw7mhh': {'ar': 'الرجوع', 'en': 'Back'},
    'c2ffav0n': {'ar': 'Home', 'en': 'Home'},
  },
  // updatapass
  {
    '9b2ax3sp': {'ar': 'انشاء كلمه مرور جديده', 'en': 'Create a new password'},
    '0ui5iuwx': {
      'ar': 'قم بكتابه كلمه المرور الجديده',
      'en': 'Type the new password',
    },
    'y8ggx3bl': {'ar': 'كلمه المرور  الجديده', 'en': 'New Password'},
    'tassz4vh': {'ar': 'كلمه المرور  الجديده', 'en': 'New Password'},
    'vwg08kil': {'ar': 'تأكيد كلمة المرور', 'en': 'Confirm password'},
    'qnwcz80u': {'ar': 'تغير كلمه السر', 'en': 'Change password'},
    'seoquzba': {'ar': 'Home', 'en': 'Home'},
  },
  // sendNotification
  {
    'v0p0haum': {'ar': 'العنوان', 'en': 'the address'},
    'lgesda43': {'ar': 'الوصف', 'en': 'Description'},
    '9eyjw7n8': {'ar': 'رفع صوره الاشعار', 'en': 'Upload notification images'},
    '4xu4hbeg': {'ar': 'ارسال الاشعار', 'en': 'Send notification'},
    'loc2oieo': {'ar': 'حذف', 'en': 'delete'},
    'pzlhgaq7': {'ar': 'ارسال الاشعارات', 'en': 'Send notifications'},
    'j17csca5': {'ar': 'Home', 'en': 'Home'},
  },
  // Omalaa
  {
    'ht772lr1': {'ar': 'الاسم', 'en': 'the name'},
    'non0m5i5': {'ar': 'الايميل', 'en': 'Email'},
    '7rb8tz8z': {'ar': 'رقم الهاتف', 'en': 'phone number'},
    '2ghmeh6w': {'ar': 'تاريخ الانشاء', 'en': 'Date of creation'},
    'j2er9fvr': {'ar': 'قائمه العملاء', 'en': 'Customer list'},
    '9mznolu6': {'ar': 'Home', 'en': 'Home'},
  },
  // adminss
  {
    'cax519g3': {'ar': 'حذف', 'en': 'delete'},
    'l89smqzv': {'ar': 'المسؤلين', 'en': 'Officials'},
    'u6m611tc': {'ar': 'Home', 'en': 'Home'},
  },
  // DarkLightSwitchLarge
  {
    'j6yhc3fl': {'ar': 'Light Mode', 'en': 'Light Mode'},
    'uqz99uql': {'ar': 'Dark Mode', 'en': 'Dark Mode'},
  },
  // addlouts
  {
    'h54pyk9v': {'ar': 'عنوان اللوط', 'en': 'Lot address'},
    'cwjfq3ed': {'ar': 'عنوان اللوط', 'en': 'Lot address'},
    'qlqa010f': {'ar': 'رقم المزاد', 'en': 'Auction number'},
    '6woznvp8': {'ar': 'تحميل صوره اللوط', 'en': 'Download the image of Lot'},
    'jhqrqq67': {'ar': 'اضافه اللوط', 'en': 'Add lot'},
  },
  // addmazadb
  {
    '8gf5l602': {'ar': 'عنوان المزاد', 'en': 'Auction Title'},
    'kyp0ost1': {'ar': 'TextField', 'en': 'TextField'},
    '6fm8fms7': {'ar': 'عدد اللوطات', 'en': 'Number of lots'},
    'd0fen7n2': {'ar': 'TextField', 'en': 'TextField'},
    'ej7tgk8x': {'ar': 'تصنيف المزاد', 'en': 'Auction classification'},
    'irosdk9u': {'ar': 'Option 1', 'en': 'Option 1'},
    'vlcvyafd': {'ar': 'Option 2', 'en': 'Option 2'},
    'pjcedqs3': {'ar': 'Option 3', 'en': 'Option 3'},
    '1d0m8wex': {'ar': 'تحميل صوره المزاد', 'en': 'Download auction image'},
    'oi4n6kxx': {
      'ar': 'تحميل كراسه الشروط',
      'en': 'Download the terms and conditions booklet',
    },
    'cz1d5j1v': {'ar': 'اضافه المزاد', 'en': 'Add auction'},
  },
  // noti
  {
    'iv7nsdcc': {'ar': 'الاشعارات', 'en': 'Notifications'},
  },
  // passupdatesuccess
  {
    '38jzx0gu': {
      'ar': 'تم تغير كلمه المرور بنجاح توجه الي التطبيق و قم بتسجيل الدخول ',
      'en':
          'The password has been changed successfully. Go to the application and log in.',
    },
  },
  // termsaccepeted
  {
    'afvezhkf': {
      'ar':
          '📄 شروط وأحكام الاستخدام\nآخر تحديث: 28 يونيو 2025\n\nيرجى قراءة هذه الشروط والأحكام بعناية قبل استخدام تطبيق \"الخبير  للمزادات\".\n\n1. مقدمة\nتطبيق \"الخبير  للمزادات\" هو منصة إلكترونية لعرض معلومات حول المزادات العقارية، الصناعية، والتجارية التي يتم تنظيمها من خلال جهة مرخصة في مصر. باستخدامك للتطبيق، فإنك توافق على الالتزام بهذه الشروط.\n\n2. طبيعة الخدمة\nالتطبيق يتيح للمستخدمين تصفح معلومات عامة عن المزادات مثل الصور، التفاصيل، مواعيد الانعقاد، وأماكن المعاينة.\n\nلا يتم إجراء المزايدة أو البيع أو الدفع الإلكتروني داخل التطبيق.\n\nالتطبيق لا يُعد وسيطًا ماليًا أو منصة للمعاملات الإلكترونية.\n\n3. تسجيل المستخدم\nيمكن للمستخدم التصفح دون تسجيل، مع إمكانية إنشاء حساب للوصول إلى مزايا إضافية مثل تحميل كراسة الشروط.\n\nالمستخدم مسؤول عن صحة ودقة البيانات التي يقدمها عند التسجيل.\n\n4. إخلاء المسؤولية\nالتطبيق لا يتحمل مسؤولية أي قرارات تتخذ بناءً على المعلومات المعروضة.\n\nلمزيد من التفاصيل حول المزادات، يُرجى التواصل مع الجهة المنظمة للمزاد المذكور داخل التطبيق.\n\n5. حقوق النشر والملكية\nجميع المحتويات داخل التطبيق (الصور، النصوص، التصميمات) مملوكة لتطبيق \"الخبير  للمزادات\"، ويُمنع استخدامها دون إذن مسبق.\n\n6. التحديثات\nيحتفظ فريق إدارة التطبيق بحق تعديل أو تحديث المحتوى أو الوظائف في أي وقت دون إشعار مسبق.\n\n7. تعديل الشروط\nقد يتم تعديل هذه الشروط من وقت لآخر. استمرار استخدامك للتطبيق يعني موافقتك على النسخة المحدثة منها.\n\n8. التواصل\nللاستفسارات أو الدعم، يُرجى التواصل معنا عبر:\n📞 *********** / *********** / ***********\n',
      'en':
          '📄 Terms and Conditions of Use\nLast updated: June 28, 2025\n\nPlease read these terms and conditions carefully before using the \"Al-Khabir Auctions\" application.\n\n1. Introduction\nThe \"Al-Khabir Auctions\" application is an electronic platform for displaying information about real estate, industrial, and commercial auctions organized by a licensed entity in Egypt. By using the application, you agree to be bound by these terms.\n\n2. Nature of Service\nThe application allows users to browse general information about auctions, such as images, details, dates, and viewing locations.\n\nNo bidding, selling, or electronic payments take place within the application.\n\nThe application is not a financial intermediary or platform for electronic transactions.\n\n3. User Registration\nUsers can browse without registering, but can create an account to access additional features such as downloading the terms and conditions booklet.\n\nThe user is responsible for the accuracy and validity of the information they provide upon registration.\n\n4. Disclaimer\nThe application is not responsible for any decisions made based on the information displayed.\n\nFor more details about auctions, please contact the auction organizer mentioned within the app.\n\n5. Copyright and Ownership\nAll content within the app (images, texts, designs) is owned by the \"Al-Khabir Auctions\" app and may not be used without prior permission.\n\n6. Updates\nThe app management team reserves the right to modify or update content or functionality at any time without prior notice.\n\n7. Amendments to Terms\nThese terms may be modified from time to time. Your continued use of the app constitutes your acceptance of the updated version.\n\n8. Communication\nFor inquiries or support, please contact us via:\n📞 *********** / *********** / ***********',
    },
    '32s90hbt': {'ar': 'موافق', 'en': 'OK'},
  },
  // anonjustcreateacc
  {
    'kcv1wkkz': {
      'ar': 'للاطلاع على كراسه الشروط يرجى انشاء حساب ',
      'en': 'To view the terms and conditions, please create an account.',
    },
    '06t58zq2': {
      'ar': 'يمكنك انشاء حساب من هنا ⬇',
      'en': 'You can create an account from here ⬇',
    },
    'wh53r08g': {'ar': 'انشاء حساب', 'en': 'Create an account'},
  },
  // updatestatse
  {
    'u4lqaivb': {'ar': 'تعديل حاله المزاد', 'en': 'Modify auction status'},
  },
  // Miscellaneous
  {
    'peigldt7': {'ar': '', 'en': ''},
    'rbpeoeaz': {'ar': '', 'en': ''},
    's9h3mkvr': {'ar': 'Enable Notifications', 'en': 'Enable Notifications'},
    '0be4nb36': {'ar': '', 'en': ''},
    'gn61i3ki': {'ar': '', 'en': ''},
    '3kq23fh0': {'ar': '', 'en': ''},
    'k8e8smav': {'ar': '', 'en': ''},
    'e9yv6oxq': {'ar': '', 'en': ''},
    '6ky04smm': {'ar': '', 'en': ''},
    'ss67swbo': {'ar': '', 'en': ''},
    'euopa730': {'ar': '', 'en': ''},
    's23ecumg': {'ar': '', 'en': ''},
    'gv1gjzmd': {'ar': '', 'en': ''},
    '60r8gcyu': {'ar': '', 'en': ''},
    'n3jbf16j': {'ar': '', 'en': ''},
    'en73vb7j': {'ar': '', 'en': ''},
    'jwneu256': {'ar': '', 'en': ''},
    'o9282y5v': {'ar': '', 'en': ''},
    'euavg7iu': {'ar': '', 'en': ''},
    'vpmpp5u6': {'ar': '', 'en': ''},
    'yiz0baeg': {'ar': '', 'en': ''},
    '7sz551nc': {'ar': '', 'en': ''},
    '42q1hd0s': {'ar': '', 'en': ''},
    'ca0wvngy': {'ar': '', 'en': ''},
    'zosctqki': {'ar': '', 'en': ''},
    'zduaacel': {'ar': '', 'en': ''},
    'm2plxjqm': {'ar': '', 'en': ''},
    'v9dpptfq': {'ar': '', 'en': ''},
  },
].reduce((a, b) => a..addAll(b));
