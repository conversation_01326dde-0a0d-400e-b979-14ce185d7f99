import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'dart:math';
import 'dart:ui';
import 'dark_light_switch_large_widget.dart' show DarkLightSwitchLargeWidget;
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class DarkLightSwitchLargeModel
    extends FlutterFlowModel<DarkLightSwitchLargeWidget> {
  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
