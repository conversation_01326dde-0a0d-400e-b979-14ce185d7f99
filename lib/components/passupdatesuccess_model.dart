import '/flutter_flow/flutter_flow_animations.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'dart:math';
import 'passupdatesuccess_widget.dart' show PassupdatesuccessWidget;
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

class PassupdatesuccessModel extends FlutterFlowModel<PassupdatesuccessWidget> {
  @override
  void initState(BuildContext context) {}

  @override
  void dispose() {}
}
