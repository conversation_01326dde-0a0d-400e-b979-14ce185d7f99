import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

Future initFirebase() async {
  if (kIsWeb) {
    await Firebase.initializeApp(
      options: FirebaseOptions(
        apiKey: "AIzaSyA-Wb-MSFn7iodXhMb9TFCG6xrciKc6X2c",
        authDomain: "auctions-guide-egypt.firebaseapp.com",
        projectId: "auctions-guide-egypt",
        storageBucket: "auctions-guide-egypt.firebasestorage.app",
        messagingSenderId: "************",
        appId: "1:************:web:e4d4fecf420e84631a2950",
      ),
    );
  } else {
    await Firebase.initializeApp();
  }
}
