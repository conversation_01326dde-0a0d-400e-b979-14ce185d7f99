import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

Future initFirebase() async {
  if (kIsWeb) {
    await Firebase.initializeApp(
        options: FirebaseOptions(
            apiKey: "AIzaSyA-Wb-MSFn7iodXhMb9TFCG6xrciKc6X2c",
            authDomain: "al-khabeer-3c043.firebaseapp.com",
            projectId: "al-khabeer-3c043",
            storageBucket: "al-khabeer-3c043.firebasestorage.app",
            messagingSenderId: "256541491441",
            appId: "1:256541491441:web:e4d4fecf420e84631a2950"));
  } else {
    await Firebase.initializeApp();
  }
}
