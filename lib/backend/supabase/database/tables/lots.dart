import '../database.dart';

class LotsTable extends SupabaseTable<LotsRow> {
  @override
  String get tableName => 'lots';

  @override
  LotsRow createRow(Map<String, dynamic> data) => LotsRow(data);
}

class LotsRow extends SupabaseDataRow {
  LotsRow(Map<String, dynamic> data) : super(data);

  @override
  SupabaseTable get table => LotsTable();

  int get id => getField<int>('id')!;
  set id(int value) => setField<int>('id', value);

  DateTime get createdAt => getField<DateTime>('created_at')!;
  set createdAt(DateTime value) => setField<DateTime>('created_at', value);

  int? get auctionid => getField<int>('auctionid');
  set auctionid(int? value) => setField<int>('auctionid', value);

  String? get title => getField<String>('title');
  set title(String? value) => setField<String>('title', value);

  String? get photo => getField<String>('photo');
  set photo(String? value) => setField<String>('photo', value);
}
