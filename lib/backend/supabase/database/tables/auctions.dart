import '../database.dart';

class AuctionsTable extends SupabaseTable<AuctionsRow> {
  @override
  String get tableName => 'auctions';

  @override
  AuctionsRow createRow(Map<String, dynamic> data) => AuctionsRow(data);
}

class AuctionsRow extends SupabaseDataRow {
  AuctionsRow(Map<String, dynamic> data) : super(data);

  @override
  SupabaseTable get table => AuctionsTable();

  int get id => getField<int>('id')!;
  set id(int value) => setField<int>('id', value);

  DateTime get createdAt => getField<DateTime>('created_at')!;
  set createdAt(DateTime value) => setField<DateTime>('created_at', value);

  String? get title => getField<String>('title');
  set title(String? value) => setField<String>('title', value);

  String? get photo => getField<String>('photo');
  set photo(String? value) => setField<String>('photo', value);

  DateTime? get date => getField<DateTime>('date');
  set date(DateTime? value) => setField<DateTime>('date', value);

  String? get status => getField<String>('status');
  set status(String? value) => setField<String>('status', value);

  double? get lotsCount => getField<double>('Lots Count');
  set lotsCount(double? value) => setField<double>('Lots Count', value);

  String? get pdf => getField<String>('PDF');
  set pdf(String? value) => setField<String>('PDF', value);

  List<String> get category => getListField<String>('category');
  set category(List<String>? value) => setListField<String>('category', value);
}
