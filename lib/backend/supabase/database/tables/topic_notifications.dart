import '../database.dart';

class TopicNotificationsTable extends SupabaseTable<TopicNotificationsRow> {
  @override
  String get tableName => 'topic_notifications';

  @override
  TopicNotificationsRow createRow(Map<String, dynamic> data) =>
      TopicNotificationsRow(data);
}

class TopicNotificationsRow extends SupabaseDataRow {
  TopicNotificationsRow(Map<String, dynamic> data) : super(data);

  @override
  SupabaseTable get table => TopicNotificationsTable();

  int get id => getField<int>('id')!;
  set id(int value) => setField<int>('id', value);

  DateTime get createdAt => getField<DateTime>('created_at')!;
  set createdAt(DateTime value) => setField<DateTime>('created_at', value);

  String get title => getField<String>('title')!;
  set title(String value) => setField<String>('title', value);

  String get body => getField<String>('body')!;
  set body(String value) => setField<String>('body', value);

  String? get imageUrl => getField<String>('image_url');
  set imageUrl(String? value) => setField<String>('image_url', value);

  String? get topic => getField<String>('topic');
  set topic(String? value) => setField<String>('topic', value);
}
