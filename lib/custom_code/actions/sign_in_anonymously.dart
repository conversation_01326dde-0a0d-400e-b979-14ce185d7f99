// Automatic FlutterFlow imports
import '/backend/backend.dart';
import '/backend/supabase/supabase.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'index.dart'; // Imports other custom actions
import 'package:flutter/material.dart';
// Begin custom action code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

import 'package:supabase_flutter/supabase_flutter.dart';

Future<void> signInAnonymously() async {
  final supabase = Supabase.instance.client;
  final response = await supabase.auth.signInAnonymously();

  final user = response.user;

  if (user != null) {
    print('Signed in as anonymous user: ${user.id}');
  } else {
    print('Failed to sign in anonymously.');
  }
}
