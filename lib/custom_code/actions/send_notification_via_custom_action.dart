// Automatic FlutterFlow imports
import '/backend/backend.dart';
import '/backend/supabase/supabase.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'index.dart'; // Imports other custom actions
import 'package:flutter/material.dart';
// Begin custom action code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

import 'dart:convert';
import 'package:http/http.dart' as http;

Future<bool> sendNotificationViaCustomAction(
  String topic,
  String title,
  String body,
  String? imageUrl,
  String apiKey,
  String functionUrl,
  String authToken, //
) async {
  // Basic check for the received token (Good to keep)
  if (authToken == null || authToken.isEmpty) {
    print(
        'CustomAction Error: AuthToken parameter is missing or empty. Ensure user is logged in and token is passed correctly.');
    return false;
  }

  // Build the payload
  final Map<String, dynamic> payload = {
    'topic': topic,
    'title': title,
    'body': body,
  };
  if (imageUrl != null && imageUrl.isNotEmpty) {
    payload['imageUrl'] = imageUrl;
  }

  // Build the headers
  final Map<String, String> headers = {
    'Content-Type': 'application/json',
    'apikey': apiKey,
    'Authorization': 'Bearer ' + authToken, // Use the passed token
  };

  // Parse the URI (Good practice to keep the try-catch)
  Uri? parsedUri;
  try {
    parsedUri = Uri.parse(functionUrl);
  } catch (e) {
    print(
        'CustomAction FATAL ERROR parsing function URL: $functionUrl - Error: $e');
    return false;
  }

  // Attempt the HTTP POST request
  try {
    // print('Custom Action: Attempting http.post to $functionUrl...'); // Optional: uncomment for fine-grained debugging if needed

    final response = await http.post(
      parsedUri,
      headers: headers,
      body: jsonEncode(payload),
    );

    // Log the outcome (status code is useful for monitoring)
    print(
        'CustomAction: Sent notification request. Response Status: ${response.statusCode}');

    // Return success based on 2xx status code
    return (response.statusCode >= 200 && response.statusCode < 300);
  } catch (e) {
    // Log any exception during the HTTP call
    print('CustomAction: FAILED during http.post - Exception: $e');
    return false;
  }
}
