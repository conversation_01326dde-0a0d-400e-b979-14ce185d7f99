// Automatic FlutterFlow imports
import '/backend/backend.dart';
import '/backend/supabase/supabase.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'index.dart'; // Imports other custom actions
import 'package:flutter/material.dart';
// Begin custom action code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

Future setFCMToken() async {
  await Firebase.initializeApp();

  FirebaseMessaging messaging = FirebaseMessaging.instance;
  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    badge: true,
    sound: true,
  );

  FFAppState().fcmToken = "About to check PN permissions";

  if (settings.authorizationStatus == AuthorizationStatus.authorized) {
    FFAppState().fcmToken = "authorizing works";
    try {
      String? fcmToken = await messaging.getToken();
      FFAppState().fcmToken = fcmToken ?? "";
    } catch (e) {
      FFAppState().fcmToken = e.toString();
    }
  }
}
