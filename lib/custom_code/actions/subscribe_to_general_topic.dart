// Automatic FlutterFlow imports
import '/backend/backend.dart';
import '/backend/supabase/supabase.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_util.dart';
import 'index.dart'; // Imports other custom actions
import 'package:flutter/material.dart';
// Begin custom action code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

import 'package:firebase_messaging/firebase_messaging.dart';

const String generalTopic = 'all_users'; //

Future subscribeToGeneralTopic() async {
  try {
    // await Firebase.initializeApp();
    await FirebaseMessaging.instance.subscribeToTopic(generalTopic);
    print('Successfully subscribed to topic: $generalTopic');
  } catch (e) {
    print('Error subscribing to topic $generalTopic: $e');
  }
}
