name: auctions_guide_egypt
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.8+10

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  app_links: 6.3.2
  app_links_platform_interface: 2.0.2
  auto_size_text: 3.0.0
  cached_network_image: 3.4.1
  cached_network_image_platform_interface: 4.1.1
  cached_network_image_web: 1.3.1
  cloud_firestore: 5.5.0
  cloud_firestore_platform_interface: 6.5.0
  cloud_firestore_web: 4.3.4
  collection: 1.19.0
  csslib: 0.17.3
  data_table_2: 2.5.10
  emoji_flag_converter: 1.1.0
  file_picker: 8.1.2
  firebase_core: 3.8.0
  firebase_core_platform_interface: 5.3.0
  firebase_core_web: 2.18.1
  firebase_messaging:
  firebase_performance: 0.10.0+10
  firebase_performance_platform_interface: 0.1.4+46
  firebase_performance_web: 0.1.7+4
  floating_bottom_navigation_bar: 1.5.2
  flutter_animate: 4.5.0
  flutter_cache_manager: 3.4.1
  flutter_plugin_android_lifecycle: 2.0.20
  font_awesome_flutter: 10.7.0
  from_css_color: 2.0.0
  functions_client: 2.3.2
  go_router: 12.1.3
  google_fonts: 6.1.0
  google_sign_in: 6.2.1
  google_sign_in_android: 6.1.30
  google_sign_in_ios: 5.7.7
  google_sign_in_platform_interface: 2.4.5
  google_sign_in_web: 0.12.4+2
  gotrue: 2.8.4
  hive: 2.2.3
  html: 0.15.4
  image_picker: 1.1.2
  image_picker_android: 0.8.12+13
  image_picker_for_web: 3.0.5
  image_picker_ios: 0.8.12
  image_picker_linux: 0.2.1+1
  image_picker_macos: 0.2.1+1
  image_picker_platform_interface: 2.10.0
  image_picker_windows: 0.2.1+1
  intl: 0.19.0
  json_path: 0.7.2
  mime_type: 1.0.0
  page_transition: 2.1.0
  path_provider: 2.1.4
  path_provider_android: 2.2.10
  path_provider_foundation: 2.4.0
  path_provider_linux: 2.2.1
  path_provider_platform_interface: 2.1.2
  path_provider_windows: 2.3.0
  photo_view: 0.15.0
  plugin_platform_interface: 2.1.8
  postgrest: 2.1.4
  provider: 6.1.2
  realtime_client: 2.2.1
  rxdart: 0.27.7
  shared_preferences: 2.3.2
  shared_preferences_android: 2.3.2
  shared_preferences_foundation: 2.5.2
  shared_preferences_linux: 2.4.1
  shared_preferences_platform_interface: 2.4.1
  shared_preferences_web: 2.4.2
  shared_preferences_windows: 2.4.1
  sign_in_with_apple: 6.1.2
  sign_in_with_apple_platform_interface: 1.1.0
  sign_in_with_apple_web: 2.1.0
  sqflite: 2.3.3+1
  sqflite_common: 2.5.4+3
  storage_client: 2.0.3
  stream_transform: 2.1.0
  supabase: 2.3.0
  supabase_flutter: 2.6.0
  table_calendar: 3.1.1
  timeago: 3.6.1
  url_launcher: 6.3.0
  url_launcher_android: 6.3.10
  url_launcher_ios: 6.3.1
  url_launcher_linux: 3.2.0
  url_launcher_macos: 3.2.1
  url_launcher_platform_interface: 2.3.2
  url_launcher_web: 2.3.3
  url_launcher_windows: 3.1.2
  video_player: 2.9.2
  video_player_android: 2.7.13
  video_player_avfoundation: 2.6.2
  video_player_platform_interface: 6.2.3
  video_player_web: 2.3.2
  webview_flutter: 4.9.0
  webview_flutter_android: 3.16.7
  webview_flutter_platform_interface: 2.10.0
  webview_flutter_wkwebview: 3.15.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.0

dependency_overrides:
  http: 1.2.2
  rxdart: 0.27.7
  uuid: ^4.0.0
  win32: 5.5.1

dev_dependencies:
  flutter_launcher_icons: 0.13.1
  flutter_lints: 4.0.0
  image: 4.2.0
  lints: 4.0.0
  flutter_test:
    sdk: flutter


flutter_launcher_icons:
  android: 'launcher_icon'
  ios: true
  web:
    generate: true
  image_path: 'assets/images/app_launcher_icon.png'
  adaptive_icon_background: '#1e1e1e'
  adaptive_icon_foreground: 'assets/images/adaptive_foreground_icon.png'


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/
    - assets/images/
    - assets/videos/
    - assets/audios/
    - assets/rive_animations/
    - assets/pdfs/
    - assets/jsons/



  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

